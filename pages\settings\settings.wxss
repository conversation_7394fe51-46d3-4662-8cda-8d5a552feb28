/* pages/settings/settings.wxss */
.container {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 头部 */
.header-section {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  padding: 40rpx 30rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: 700;
}

.tank-selector {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 16rpx 24rpx;
  border-radius: 30rpx;
}

.tank-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.tank-name {
  font-size: 24rpx;
}

/* 卡片样式 */
.card {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.clear-btn {
  color: #dc3545;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border: 1rpx solid #dc3545;
  border-radius: 20rpx;
}

/* 预警开关 */
.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.switch-item:last-child {
  border-bottom: none;
}

.switch-info {
  flex: 1;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.switch-desc {
  font-size: 22rpx;
  color: #666;
}

/* 参数设置 */
.threshold-section {
  margin: 20rpx 30rpx;
}

.param-setting-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.param-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.param-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.param-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.param-unit {
  font-size: 22rpx;
  color: #666;
}

.threshold-settings {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.threshold-item {
  margin-bottom: 30rpx;
}

.threshold-item:last-child {
  margin-bottom: 0;
}

.threshold-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 26rpx;
  color: #333;
}

.current-value {
  font-size: 22rpx;
  color: #4A90E2;
  font-weight: 500;
}

.threshold-controls {
  padding: 0 20rpx;
}

.optimal-range {
  background: rgba(74, 144, 226, 0.1);
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  margin-top: 20rpx;
  text-align: center;
}

.range-label {
  font-size: 22rpx;
  color: #666;
}

.range-value {
  font-size: 22rpx;
  color: #4A90E2;
  font-weight: 500;
}

/* 预警统计 */
.alert-stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #4A90E2;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

/* 预警列表 */
.alert-list {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid;
}

.alert-warning {
  background: rgba(255, 193, 7, 0.1);
  border-left-color: #ffc107;
}

.alert-danger {
  background: rgba(220, 53, 69, 0.1);
  border-left-color: #dc3545;
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.alert-time {
  font-size: 20rpx;
  color: #666;
}

.resolve-btn {
  padding: 12rpx 20rpx;
  background: #4A90E2;
  color: white;
  border-radius: 20rpx;
  font-size: 22rpx;
  transition: all 0.3s ease;
}

.resolve-btn.resolved {
  background: #28a745;
}

.no-alerts {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.no-alerts-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 16rpx;
}

.no-alerts-text {
  font-size: 24rpx;
}

/* 通知设置 */
.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-info {
  flex: 1;
}

.notification-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.notification-desc {
  font-size: 22rpx;
  color: #666;
}

.picker-value {
  color: #4A90E2;
  font-size: 26rpx;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 1rpx solid #dee2e6;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
}

.btn-primary, .btn-secondary {
  flex: 1;
  padding: 24rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}
