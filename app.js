// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },
  globalData: {
    userInfo: null,
    // 当前选择的水族箱类型
    currentTankType: 'freshwater', // freshwater, planted, turtle
    // 水族箱配置
    tankTypes: {
      freshwater: {
        name: '淡水缸',
        icon: '🐠',
        color: '#4A90E2',
        params: ['temperature', 'ph', 'oxygen', 'turbidity', 'waterLevel']
      },
      planted: {
        name: '草缸',
        icon: '🌱',
        color: '#7ED321',
        params: ['temperature', 'ph', 'co2', 'light', 'nutrients', 'waterLevel']
      },
      turtle: {
        name: '龟缸',
        icon: '🐢',
        color: '#F5A623',
        params: ['temperature', 'humidity', 'uvb', 'waterLevel', 'basking']
      }
    },
    // 参数配置
    paramConfig: {
      temperature: { name: '水温', unit: '°C', icon: '🌡️', min: 20, max: 30, optimal: [24, 26] },
      ph: { name: 'pH值', unit: '', icon: '⚗️', min: 6.0, max: 8.5, optimal: [6.5, 7.5] },
      oxygen: { name: '溶氧量', unit: 'mg/L', icon: '💨', min: 5, max: 12, optimal: [7, 9] },
      turbidity: { name: '浊度', unit: 'NTU', icon: '💧', min: 0, max: 10, optimal: [0, 2] },
      co2: { name: 'CO₂浓度', unit: 'mg/L', icon: '🫧', min: 10, max: 30, optimal: [15, 25] },
      light: { name: '光照强度', unit: 'lux', icon: '💡', min: 1000, max: 5000, optimal: [2000, 3500] },
      nutrients: { name: '营养液', unit: 'ppm', icon: '🧪', min: 5, max: 50, optimal: [10, 30] },
      humidity: { name: '湿度', unit: '%', icon: '💦', min: 60, max: 90, optimal: [70, 80] },
      uvb: { name: 'UVB强度', unit: 'μW/cm²', icon: '☀️', min: 10, max: 100, optimal: [30, 50] },
      waterLevel: { name: '水位', unit: 'cm', icon: '📏', min: 10, max: 50, optimal: [30, 40] },
      basking: { name: '晒台温度', unit: '°C', icon: '🔥', min: 25, max: 35, optimal: [28, 32] }
    }
  }
})
