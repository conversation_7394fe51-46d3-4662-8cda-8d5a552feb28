// utils/mockData.js - 模拟数据服务

const app = getApp()

// 生成随机数值
function randomValue(min, max, decimals = 1) {
  const value = Math.random() * (max - min) + min
  return Number(value.toFixed(decimals))
}

// 生成正态分布随机数（用于更真实的数据波动）
function normalRandom(mean, stdDev) {
  let u = 0, v = 0;
  while(u === 0) u = Math.random(); // Converting [0,1) to (0,1)
  while(v === 0) v = Math.random();
  const z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
  return z * stdDev + mean;
}

// 获取参数状态（正常/警告/危险）
function getParamStatus(value, config) {
  const { optimal, min, max } = config
  if (value < min || value > max) {
    return 'danger'
  }
  if (value < optimal[0] || value > optimal[1]) {
    return 'warning'
  }
  return 'normal'
}

// 生成实时数据
function generateRealtimeData(tankType) {
  const tankConfig = app.globalData.tankTypes[tankType]
  const paramConfig = app.globalData.paramConfig
  const data = {}
  
  tankConfig.params.forEach(paramKey => {
    const config = paramConfig[paramKey]
    let value
    
    // 根据参数类型生成不同的模拟数据
    switch(paramKey) {
      case 'temperature':
        // 温度有日夜波动
        const hour = new Date().getHours()
        const baseTemp = (config.optimal[0] + config.optimal[1]) / 2
        const dailyVariation = Math.sin((hour - 6) * Math.PI / 12) * 1.5
        value = normalRandom(baseTemp + dailyVariation, 0.3)
        break
        
      case 'ph':
        // pH值相对稳定
        const basePh = (config.optimal[0] + config.optimal[1]) / 2
        value = normalRandom(basePh, 0.1)
        break
        
      case 'light':
        // 光照强度根据时间变化
        const currentHour = new Date().getHours()
        if (currentHour >= 8 && currentHour <= 20) {
          // 白天光照
          value = normalRandom((config.optimal[0] + config.optimal[1]) / 2, 200)
        } else {
          // 夜间光照
          value = randomValue(0, 500)
        }
        break
        
      case 'co2':
        // CO2浓度在光照期间消耗
        const lightHour = new Date().getHours()
        const baseCo2 = (config.optimal[0] + config.optimal[1]) / 2
        if (lightHour >= 8 && lightHour <= 20) {
          value = normalRandom(baseCo2 - 3, 2)
        } else {
          value = normalRandom(baseCo2 + 2, 2)
        }
        break
        
      default:
        // 其他参数使用正态分布
        const baseValue = (config.optimal[0] + config.optimal[1]) / 2
        const variation = (config.optimal[1] - config.optimal[0]) * 0.2
        value = normalRandom(baseValue, variation)
    }
    
    // 确保值在合理范围内
    value = Math.max(config.min, Math.min(config.max, value))
    
    // 根据参数类型决定小数位数
    const decimals = paramKey === 'ph' ? 1 : (value > 100 ? 0 : 1)
    value = Number(value.toFixed(decimals))
    
    data[paramKey] = {
      value: value,
      status: getParamStatus(value, config),
      timestamp: Date.now()
    }
  })
  
  return data
}

// 生成历史数据
function generateHistoryData(tankType, days = 7) {
  const tankConfig = app.globalData.tankTypes[tankType]
  const paramConfig = app.globalData.paramConfig
  const history = []
  
  const now = Date.now()
  const interval = 30 * 60 * 1000 // 30分钟间隔
  const totalPoints = days * 24 * 2 // 每天48个数据点
  
  for (let i = totalPoints; i >= 0; i--) {
    const timestamp = now - (i * interval)
    const date = new Date(timestamp)
    const hour = date.getHours()
    
    const dataPoint = {
      timestamp: timestamp,
      date: date.toISOString().split('T')[0],
      time: date.toTimeString().split(' ')[0].substring(0, 5),
      data: {}
    }
    
    tankConfig.params.forEach(paramKey => {
      const config = paramConfig[paramKey]
      let baseValue = (config.optimal[0] + config.optimal[1]) / 2
      let value
      
      // 添加时间相关的变化
      switch(paramKey) {
        case 'temperature':
          const dailyVariation = Math.sin((hour - 6) * Math.PI / 12) * 1.5
          value = normalRandom(baseValue + dailyVariation, 0.5)
          break
          
        case 'light':
          if (hour >= 8 && hour <= 20) {
            value = normalRandom(baseValue, 300)
          } else {
            value = randomValue(0, 800)
          }
          break
          
        case 'co2':
          if (hour >= 8 && hour <= 20) {
            value = normalRandom(baseValue - 2, 3)
          } else {
            value = normalRandom(baseValue + 1, 2)
          }
          break
          
        default:
          const variation = (config.optimal[1] - config.optimal[0]) * 0.3
          value = normalRandom(baseValue, variation)
      }
      
      value = Math.max(config.min, Math.min(config.max, value))
      const decimals = paramKey === 'ph' ? 1 : (value > 100 ? 0 : 1)
      value = Number(value.toFixed(decimals))
      
      dataPoint.data[paramKey] = {
        value: value,
        status: getParamStatus(value, config)
      }
    })
    
    history.push(dataPoint)
  }
  
  return history
}

// 生成预警记录
function generateAlerts(tankType, days = 30) {
  const alerts = []
  const now = Date.now()
  const alertTypes = ['high', 'low', 'offline', 'maintenance']
  const tankConfig = app.globalData.tankTypes[tankType]
  const paramConfig = app.globalData.paramConfig
  
  // 随机生成一些预警记录
  const alertCount = randomValue(5, 15, 0)
  
  for (let i = 0; i < alertCount; i++) {
    const timestamp = now - randomValue(0, days * 24 * 60 * 60 * 1000, 0)
    const paramKey = tankConfig.params[Math.floor(Math.random() * tankConfig.params.length)]
    const alertType = alertTypes[Math.floor(Math.random() * alertTypes.length)]
    const config = paramConfig[paramKey]
    
    let message = ''
    let level = 'warning'
    
    switch(alertType) {
      case 'high':
        message = `${config.name}过高 (${randomValue(config.optimal[1], config.max)}${config.unit})`
        level = 'danger'
        break
      case 'low':
        message = `${config.name}过低 (${randomValue(config.min, config.optimal[0])}${config.unit})`
        level = 'warning'
        break
      case 'offline':
        message = `${config.name}传感器离线`
        level = 'danger'
        break
      case 'maintenance':
        message = `${config.name}传感器需要维护`
        level = 'warning'
        break
    }
    
    alerts.push({
      id: `alert_${timestamp}_${paramKey}`,
      timestamp: timestamp,
      date: new Date(timestamp).toLocaleDateString(),
      time: new Date(timestamp).toLocaleTimeString(),
      parameter: paramKey,
      parameterName: config.name,
      type: alertType,
      level: level,
      message: message,
      resolved: Math.random() > 0.3 // 70%的预警已解决
    })
  }
  
  return alerts.sort((a, b) => b.timestamp - a.timestamp)
}

module.exports = {
  generateRealtimeData,
  generateHistoryData,
  generateAlerts,
  getParamStatus
}
