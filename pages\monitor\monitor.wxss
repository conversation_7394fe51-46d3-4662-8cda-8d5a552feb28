/* pages/monitor/monitor.wxss */
.container {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 状态栏 */
.status-bar {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  padding: 40rpx 30rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tank-info {
  display: flex;
  align-items: center;
}

.tank-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.tank-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.last-update {
  font-size: 22rpx;
  opacity: 0.8;
}

.overall-status {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 16rpx 24rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
}

/* 监控区域 */
.monitor-section {
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.auto-refresh {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  background: #f8f9fa;
  border: 2rpx solid #dee2e6;
  font-size: 22rpx;
  color: #6c757d;
  transition: all 0.3s ease;
}

.auto-refresh.active {
  background: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

.refresh-icon {
  margin-right: 8rpx;
}

.manual-refresh {
  padding: 12rpx;
  border-radius: 50%;
  background: #4A90E2;
  color: white;
  font-size: 24rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rotating {
  animation: rotate 1s linear infinite;
}

/* 参数网格 */
.params-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20rpx;
}

.param-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-left: 8rpx solid;
  transition: all 0.3s ease;
}

.param-normal {
  border-left-color: #28a745;
}

.param-warning {
  border-left-color: #ffc107;
}

.param-danger {
  border-left-color: #dc3545;
}

.param-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 25rpx rgba(0, 0, 0, 0.12);
}

.param-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.param-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.param-info {
  flex: 1;
}

.param-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.param-range {
  font-size: 22rpx;
  color: #666;
}

.param-status-indicator {
  margin-left: 20rpx;
}

.status-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
}

.param-value-display {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24rpx;
}

.current-value {
  display: flex;
  align-items: baseline;
}

.value-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #4A90E2;
}

.value-unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

.value-trend {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #666;
}

.trend-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.trend-up {
  color: #dc3545;
}

.trend-down {
  color: #28a745;
}

.trend-stable {
  color: #6c757d;
}

/* 进度条 */
.param-progress {
  position: relative;
}

.progress-bar {
  height: 12rpx;
  background: #e9ecef;
  border-radius: 6rpx;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.5s ease;
}

.progress-normal {
  background: #28a745;
}

.progress-warning {
  background: #ffc107;
}

.progress-danger {
  background: #dc3545;
}

.optimal-range {
  position: absolute;
  top: 0;
  height: 100%;
  background: rgba(74, 144, 226, 0.3);
  border: 2rpx solid #4A90E2;
  border-radius: 6rpx;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8rpx;
  font-size: 20rpx;
  color: #999;
}

/* 快速操作面板 */
.quick-panel {
  background: white;
  margin: 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.panel-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20rpx;
}

.quick-action {
  text-align: center;
  padding: 24rpx 16rpx;
  border-radius: 16rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.quick-action:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.action-icon {
  font-size: 36rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 22rpx;
  color: #333;
}

/* 系统信息 */
.system-info {
  background: white;
  margin: 0 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 26rpx;
  color: #666;
}

.info-value {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

/* 参数详情弹窗 */
.param-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.param-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80%;
  overflow: hidden;
  transform: translateY(50rpx);
  transition: transform 0.3s ease;
}

.param-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  background: #f8f9fa;
}

.modal-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-title .param-icon {
  margin-right: 16rpx;
  font-size: 36rpx;
}

.close-btn {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
}

.modal-body {
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.current-reading {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.reading-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.reading-value {
  margin-bottom: 16rpx;
}

.reading-value .value {
  font-size: 64rpx;
  font-weight: 700;
  color: #4A90E2;
}

.reading-value .unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 8rpx;
}

.reading-status {
  font-size: 26rpx;
  font-weight: 500;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  display: inline-block;
}

.param-ranges {
  margin-bottom: 40rpx;
}

.range-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.range-item:last-child {
  border-bottom: none;
}

.range-item.optimal {
  background: rgba(74, 144, 226, 0.1);
  padding: 20rpx;
  border-radius: 12rpx;
  border: none;
  margin-top: 16rpx;
}

.range-label {
  font-size: 26rpx;
  color: #666;
}

.range-value {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.recent-trend {
  margin-top: 20rpx;
}

.trend-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.trend-chart {
  display: flex;
  align-items: flex-end;
  height: 120rpx;
  gap: 4rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.trend-point {
  flex: 1;
  min-height: 8rpx;
  border-radius: 2rpx;
  transition: all 0.3s ease;
}
