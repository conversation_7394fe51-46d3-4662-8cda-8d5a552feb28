// pages/monitor/monitor.js
const mockData = require('../../utils/mockData.js')

Page({
  data: {
    tankConfig: {},
    monitorData: [],
    overallStatus: 'normal',
    overallStatusText: '正常',
    lastUpdateTime: '',
    autoRefresh: true,
    refreshing: false,
    updateInterval: 30,
    deviceStatus: 'normal',
    deviceStatusText: '在线',
    networkStatus: 'normal',
    networkStatusText: '已连接',
    uptime: '0天0小时',
    showModal: false,
    modalData: {},
    startTime: Date.now()
  },

  onLoad() {
    this.initTankConfig()
    this.loadMonitorData()
    this.startAutoRefresh()
    this.updateUptime()
  },

  onUnload() {
    this.stopAutoRefresh()
    if (this.uptimeTimer) {
      clearInterval(this.uptimeTimer)
    }
  },

  onShow() {
    this.loadMonitorData()
  },

  initTankConfig() {
    const app = getApp()
    const currentType = app.globalData.currentTankType
    const tankConfig = app.globalData.tankTypes[currentType]
    
    this.setData({
      tankConfig: tankConfig
    })
  },

  loadMonitorData() {
    const app = getApp()
    const currentType = app.globalData.currentTankType
    const realtimeData = mockData.generateRealtimeData(currentType)
    const paramConfig = app.globalData.paramConfig
    const monitorData = []
    
    let normalCount = 0
    let warningCount = 0
    let dangerCount = 0
    
    Object.keys(realtimeData).forEach(key => {
      const data = realtimeData[key]
      const config = paramConfig[key]
      
      // 计算进度条位置
      const range = config.max - config.min
      const progressPercent = ((data.value - config.min) / range) * 100
      const optimalStart = ((config.optimal[0] - config.min) / range) * 100
      const optimalWidth = ((config.optimal[1] - config.optimal[0]) / range) * 100
      
      // 生成趋势数据
      const trend = this.generateTrend()
      
      // 统计状态
      switch(data.status) {
        case 'warning':
          warningCount++
          break
        case 'danger':
          dangerCount++
          break
        default:
          normalCount++
      }
      
      monitorData.push({
        key: key,
        name: config.name,
        icon: config.icon,
        value: data.value,
        unit: config.unit,
        status: data.status,
        min: config.min,
        max: config.max,
        optimalMin: config.optimal[0],
        optimalMax: config.optimal[1],
        progressPercent: Math.max(0, Math.min(100, progressPercent)),
        optimalStart: optimalStart,
        optimalWidth: optimalWidth,
        trend: trend.direction,
        trendIcon: trend.icon,
        trendText: trend.text
      })
    })
    
    // 确定整体状态
    let overallStatus = 'normal'
    let overallStatusText = '正常'
    
    if (dangerCount > 0) {
      overallStatus = 'danger'
      overallStatusText = `${dangerCount}项异常`
    } else if (warningCount > 0) {
      overallStatus = 'warning'
      overallStatusText = `${warningCount}项警告`
    }
    
    this.setData({
      monitorData: monitorData,
      overallStatus: overallStatus,
      overallStatusText: overallStatusText,
      lastUpdateTime: new Date().toLocaleTimeString(),
      refreshing: false
    })
  },

  generateTrend() {
    const trends = [
      { direction: 'up', icon: '↗', text: '上升' },
      { direction: 'down', icon: '↘', text: '下降' },
      { direction: 'stable', icon: '→', text: '稳定' }
    ]
    
    return trends[Math.floor(Math.random() * trends.length)]
  },

  startAutoRefresh() {
    if (this.data.autoRefresh) {
      this.refreshTimer = setInterval(() => {
        this.loadMonitorData()
      }, this.data.updateInterval * 1000)
    }
  },

  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  },

  toggleAutoRefresh() {
    const autoRefresh = !this.data.autoRefresh
    this.setData({ autoRefresh })
    
    if (autoRefresh) {
      this.startAutoRefresh()
      wx.showToast({
        title: '已开启自动刷新',
        icon: 'success'
      })
    } else {
      this.stopAutoRefresh()
      wx.showToast({
        title: '已关闭自动刷新',
        icon: 'none'
      })
    }
  },

  manualRefresh() {
    this.setData({ refreshing: true })
    
    setTimeout(() => {
      this.loadMonitorData()
      wx.showToast({
        title: '数据已更新',
        icon: 'success',
        duration: 1500
      })
    }, 1000)
  },

  updateUptime() {
    this.uptimeTimer = setInterval(() => {
      const now = Date.now()
      const diff = now - this.data.startTime
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      
      let uptime = ''
      if (days > 0) {
        uptime = `${days}天${hours}小时`
      } else if (hours > 0) {
        uptime = `${hours}小时${minutes}分钟`
      } else {
        uptime = `${minutes}分钟`
      }
      
      this.setData({ uptime })
    }, 60000) // 每分钟更新一次
  },

  showParamDetail(e) {
    const paramKey = e.currentTarget.dataset.param
    const paramData = this.data.monitorData.find(item => item.key === paramKey)
    
    if (paramData) {
      // 生成模拟趋势图数据
      const trendData = []
      for (let i = 0; i < 20; i++) {
        const height = Math.random() * 80 + 10
        let color = '#4A90E2'
        if (height > 70) color = '#dc3545'
        else if (height > 50) color = '#ffc107'
        else color = '#28a745'
        
        trendData.push({ height, color })
      }
      
      const modalData = {
        ...paramData,
        statusText: this.getStatusText(paramData.status),
        trendData: trendData
      }
      
      this.setData({
        modalData: modalData,
        showModal: true
      })
    }
  },

  getStatusText(status) {
    switch(status) {
      case 'normal': return '正常'
      case 'warning': return '警告'
      case 'danger': return '危险'
      default: return '未知'
    }
  },

  hideModal() {
    this.setData({ showModal: false })
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  // 快速操作
  viewHistory() {
    wx.switchTab({
      url: '/pages/history/history'
    })
  },

  exportData() {
    wx.showActionSheet({
      itemList: ['导出今日数据', '导出本周数据', '导出本月数据'],
      success: (res) => {
        const periods = ['今日', '本周', '本月']
        wx.showLoading({
          title: `正在导出${periods[res.tapIndex]}数据...`
        })
        
        setTimeout(() => {
          wx.hideLoading()
          wx.showToast({
            title: '导出成功',
            icon: 'success'
          })
        }, 2000)
      }
    })
  },

  setAlert() {
    wx.switchTab({
      url: '/pages/settings/settings'
    })
  },

  calibrateSensor() {
    wx.showModal({
      title: '传感器校准',
      content: '是否开始传感器校准程序？校准过程需要5-10分钟。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '正在校准...'
          })
          
          setTimeout(() => {
            wx.hideLoading()
            wx.showToast({
              title: '校准完成',
              icon: 'success'
            })
          }, 3000)
        }
      }
    })
  }
})
