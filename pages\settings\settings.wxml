<!--pages/settings/settings.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header-section">
    <view class="page-title">预警设置</view>
    <view class="tank-selector">
      <text class="tank-icon">{{currentTankConfig.icon}}</text>
      <text class="tank-name">{{currentTankConfig.name}}</text>
    </view>
  </view>

  <!-- 预警开关 -->
  <view class="alert-switch-section card">
    <view class="section-title">预警功能</view>
    <view class="switch-item">
      <view class="switch-info">
        <view class="switch-label">启用预警通知</view>
        <view class="switch-desc">当参数超出设定范围时发送通知</view>
      </view>
      <switch 
        checked="{{alertEnabled}}" 
        bindchange="toggleAlert"
        color="#4A90E2"
      />
    </view>
    
    <view class="switch-item" wx:if="{{alertEnabled}}">
      <view class="switch-info">
        <view class="switch-label">声音提醒</view>
        <view class="switch-desc">预警时播放提示音</view>
      </view>
      <switch 
        checked="{{soundEnabled}}" 
        bindchange="toggleSound"
        color="#4A90E2"
      />
    </view>
    
    <view class="switch-item" wx:if="{{alertEnabled}}">
      <view class="switch-info">
        <view class="switch-label">震动提醒</view>
        <view class="switch-desc">预警时震动提醒</view>
      </view>
      <switch 
        checked="{{vibrationEnabled}}" 
        bindchange="toggleVibration"
        color="#4A90E2"
      />
    </view>
  </view>

  <!-- 参数阈值设置 -->
  <view class="threshold-section">
    <view class="section-title">参数阈值设置</view>
    
    <view 
      class="param-setting-card"
      wx:for="{{paramSettings}}"
      wx:key="key"
    >
      <view class="param-header">
        <view class="param-info">
          <text class="param-icon">{{item.icon}}</text>
          <view class="param-details">
            <view class="param-name">{{item.name}}</view>
            <view class="param-unit">单位: {{item.unit || '无'}}</view>
          </view>
        </view>
        <view class="param-status">
          <switch 
            checked="{{item.enabled}}" 
            bindchange="toggleParamAlert"
            data-param="{{item.key}}"
            color="#4A90E2"
          />
        </view>
      </view>
      
      <view class="threshold-settings" wx:if="{{item.enabled}}">
        <!-- 最小值设置 -->
        <view class="threshold-item">
          <view class="threshold-label">
            <text class="label-text">最小值预警</text>
            <text class="current-value">当前: {{item.minThreshold}}{{item.unit}}</text>
          </view>
          <view class="threshold-controls">
            <slider
              min="{{item.absoluteMin}}"
              max="{{item.optimalMin}}"
              value="{{item.minThreshold}}"
              step="{{item.step}}"
              show-value="true"
              bindchange="updateMinThreshold"
              data-param="{{item.key}}"
              activeColor="#dc3545"
              backgroundColor="#f0f0f0"
            />
          </view>
        </view>
        
        <!-- 最大值设置 -->
        <view class="threshold-item">
          <view class="threshold-label">
            <text class="label-text">最大值预警</text>
            <text class="current-value">当前: {{item.maxThreshold}}{{item.unit}}</text>
          </view>
          <view class="threshold-controls">
            <slider
              min="{{item.optimalMax}}"
              max="{{item.absoluteMax}}"
              value="{{item.maxThreshold}}"
              step="{{item.step}}"
              show-value="true"
              bindchange="updateMaxThreshold"
              data-param="{{item.key}}"
              activeColor="#dc3545"
              backgroundColor="#f0f0f0"
            />
          </view>
        </view>
        
        <!-- 适宜范围显示 -->
        <view class="optimal-range">
          <text class="range-label">适宜范围: </text>
          <text class="range-value">{{item.optimalMin}} - {{item.optimalMax}}{{item.unit}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 预警历史 -->
  <view class="alert-history-section card">
    <view class="section-header">
      <view class="section-title">预警历史</view>
      <view class="clear-btn" bindtap="clearAlertHistory">
        <text>清空</text>
      </view>
    </view>
    
    <view class="alert-stats">
      <view class="stat-item">
        <view class="stat-number">{{alertStats.total}}</view>
        <view class="stat-label">总预警</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{alertStats.unresolved}}</view>
        <view class="stat-label">未解决</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{alertStats.today}}</view>
        <view class="stat-label">今日</view>
      </view>
    </view>
    
    <scroll-view class="alert-list" scroll-y="true" style="height: 400rpx;">
      <view 
        class="alert-item alert-{{item.level}}"
        wx:for="{{alertHistory}}"
        wx:key="id"
      >
        <view class="alert-content">
          <view class="alert-message">{{item.message}}</view>
          <view class="alert-time">{{item.date}} {{item.time}}</view>
        </view>
        <view class="alert-actions">
          <view 
            class="resolve-btn {{item.resolved ? 'resolved' : ''}}"
            bindtap="toggleResolveAlert"
            data-id="{{item.id}}"
          >
            {{item.resolved ? '已解决' : '标记解决'}}
          </view>
        </view>
      </view>
      
      <view class="no-alerts" wx:if="{{alertHistory.length === 0}}">
        <text class="no-alerts-icon">🎉</text>
        <text class="no-alerts-text">暂无预警记录</text>
      </view>
    </scroll-view>
  </view>

  <!-- 通知设置 */
  <view class="notification-section card">
    <view class="section-title">通知设置</view>
    
    <view class="notification-item">
      <view class="notification-info">
        <view class="notification-label">预警延迟</view>
        <view class="notification-desc">参数异常后多久发送预警</view>
      </view>
      <picker 
        mode="selector" 
        range="{{delayOptions}}" 
        range-key="label"
        value="{{selectedDelayIndex}}"
        bindchange="changeAlertDelay"
      >
        <view class="picker-value">{{delayOptions[selectedDelayIndex].label}}</view>
      </picker>
    </view>
    
    <view class="notification-item">
      <view class="notification-info">
        <view class="notification-label">重复提醒间隔</view>
        <view class="notification-desc">未解决预警的重复提醒间隔</view>
      </view>
      <picker 
        mode="selector" 
        range="{{repeatOptions}}" 
        range-key="label"
        value="{{selectedRepeatIndex}}"
        bindchange="changeRepeatInterval"
      >
        <view class="picker-value">{{repeatOptions[selectedRepeatIndex].label}}</view>
      </picker>
    </view>
  </view>

  <!-- 操作按钮 */
  <view class="action-buttons">
    <button class="btn-secondary" bindtap="resetToDefault">恢复默认设置</button>
    <button class="btn-primary" bindtap="saveSettings">保存设置</button>
  </view>
</view>
