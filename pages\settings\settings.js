// pages/settings/settings.js
const mockData = require('../../utils/mockData.js')

Page({
  data: {
    currentTankConfig: {},
    alertEnabled: true,
    soundEnabled: true,
    vibrationEnabled: false,
    paramSettings: [],
    alertHistory: [],
    alertStats: {
      total: 0,
      unresolved: 0,
      today: 0
    },
    delayOptions: [
      { label: '立即', value: 0 },
      { label: '30秒后', value: 30 },
      { label: '1分钟后', value: 60 },
      { label: '5分钟后', value: 300 },
      { label: '10分钟后', value: 600 }
    ],
    selectedDelayIndex: 1,
    repeatOptions: [
      { label: '不重复', value: 0 },
      { label: '每5分钟', value: 300 },
      { label: '每10分钟', value: 600 },
      { label: '每30分钟', value: 1800 },
      { label: '每小时', value: 3600 }
    ],
    selectedRepeatIndex: 2
  },

  onLoad() {
    this.initTankConfig()
    this.initParamSettings()
    this.loadAlertHistory()
    this.loadSettings()
  },

  onShow() {
    // 检查是否需要更新水族箱类型
    this.initTankConfig()
    this.initParamSettings()
  },

  initTankConfig() {
    const app = getApp()
    const currentType = app.globalData.currentTankType
    const tankConfig = app.globalData.tankTypes[currentType]
    
    this.setData({
      currentTankConfig: tankConfig
    })
  },

  initParamSettings() {
    const app = getApp()
    const currentType = app.globalData.currentTankType
    const tankConfig = app.globalData.tankTypes[currentType]
    const paramConfig = app.globalData.paramConfig
    
    const paramSettings = tankConfig.params.map(paramKey => {
      const config = paramConfig[paramKey]
      
      // 计算步长
      let step = 0.1
      if (config.max - config.min > 100) {
        step = 1
      } else if (config.max - config.min > 10) {
        step = 0.5
      }
      
      return {
        key: paramKey,
        name: config.name,
        icon: config.icon,
        unit: config.unit,
        enabled: true,
        absoluteMin: config.min,
        absoluteMax: config.max,
        optimalMin: config.optimal[0],
        optimalMax: config.optimal[1],
        minThreshold: config.optimal[0] - (config.optimal[0] - config.min) * 0.5,
        maxThreshold: config.optimal[1] + (config.max - config.optimal[1]) * 0.5,
        step: step
      }
    })
    
    this.setData({
      paramSettings: paramSettings
    })
  },

  loadAlertHistory() {
    const app = getApp()
    const currentType = app.globalData.currentTankType
    const alerts = mockData.generateAlerts(currentType, 30)
    
    // 计算统计信息
    const today = new Date().toDateString()
    const todayAlerts = alerts.filter(alert => 
      new Date(alert.timestamp).toDateString() === today
    )
    const unresolvedAlerts = alerts.filter(alert => !alert.resolved)
    
    this.setData({
      alertHistory: alerts.slice(0, 20), // 只显示最近20条
      alertStats: {
        total: alerts.length,
        unresolved: unresolvedAlerts.length,
        today: todayAlerts.length
      }
    })
  },

  loadSettings() {
    // 从本地存储加载设置
    try {
      const settings = wx.getStorageSync('alertSettings')
      if (settings) {
        this.setData({
          alertEnabled: settings.alertEnabled !== undefined ? settings.alertEnabled : true,
          soundEnabled: settings.soundEnabled !== undefined ? settings.soundEnabled : true,
          vibrationEnabled: settings.vibrationEnabled !== undefined ? settings.vibrationEnabled : false,
          selectedDelayIndex: settings.selectedDelayIndex || 1,
          selectedRepeatIndex: settings.selectedRepeatIndex || 2
        })
        
        // 更新参数设置
        if (settings.paramSettings) {
          const updatedParams = this.data.paramSettings.map(param => {
            const savedParam = settings.paramSettings.find(p => p.key === param.key)
            return savedParam ? { ...param, ...savedParam } : param
          })
          this.setData({ paramSettings: updatedParams })
        }
      }
    } catch (e) {
      console.error('加载设置失败:', e)
    }
  },

  saveSettings() {
    const settings = {
      alertEnabled: this.data.alertEnabled,
      soundEnabled: this.data.soundEnabled,
      vibrationEnabled: this.data.vibrationEnabled,
      selectedDelayIndex: this.data.selectedDelayIndex,
      selectedRepeatIndex: this.data.selectedRepeatIndex,
      paramSettings: this.data.paramSettings
    }
    
    try {
      wx.setStorageSync('alertSettings', settings)
      wx.showToast({
        title: '设置已保存',
        icon: 'success'
      })
    } catch (e) {
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  },

  toggleAlert(e) {
    this.setData({
      alertEnabled: e.detail.value
    })
  },

  toggleSound(e) {
    this.setData({
      soundEnabled: e.detail.value
    })
  },

  toggleVibration(e) {
    this.setData({
      vibrationEnabled: e.detail.value
    })
  },

  toggleParamAlert(e) {
    const paramKey = e.currentTarget.dataset.param
    const enabled = e.detail.value
    
    const updatedParams = this.data.paramSettings.map(param => {
      if (param.key === paramKey) {
        return { ...param, enabled: enabled }
      }
      return param
    })
    
    this.setData({
      paramSettings: updatedParams
    })
  },

  updateMinThreshold(e) {
    const paramKey = e.currentTarget.dataset.param
    const value = e.detail.value
    
    const updatedParams = this.data.paramSettings.map(param => {
      if (param.key === paramKey) {
        return { ...param, minThreshold: value }
      }
      return param
    })
    
    this.setData({
      paramSettings: updatedParams
    })
  },

  updateMaxThreshold(e) {
    const paramKey = e.currentTarget.dataset.param
    const value = e.detail.value
    
    const updatedParams = this.data.paramSettings.map(param => {
      if (param.key === paramKey) {
        return { ...param, maxThreshold: value }
      }
      return param
    })
    
    this.setData({
      paramSettings: updatedParams
    })
  },

  toggleResolveAlert(e) {
    const alertId = e.currentTarget.dataset.id
    
    const updatedHistory = this.data.alertHistory.map(alert => {
      if (alert.id === alertId) {
        return { ...alert, resolved: !alert.resolved }
      }
      return alert
    })
    
    // 重新计算统计信息
    const unresolvedCount = updatedHistory.filter(alert => !alert.resolved).length
    
    this.setData({
      alertHistory: updatedHistory,
      'alertStats.unresolved': unresolvedCount
    })
    
    wx.showToast({
      title: '状态已更新',
      icon: 'success'
    })
  },

  clearAlertHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有预警历史记录吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            alertHistory: [],
            alertStats: {
              total: 0,
              unresolved: 0,
              today: 0
            }
          })
          
          wx.showToast({
            title: '已清空',
            icon: 'success'
          })
        }
      }
    })
  },

  changeAlertDelay(e) {
    this.setData({
      selectedDelayIndex: e.detail.value
    })
  },

  changeRepeatInterval(e) {
    this.setData({
      selectedRepeatIndex: e.detail.value
    })
  },

  resetToDefault() {
    wx.showModal({
      title: '恢复默认设置',
      content: '确定要恢复所有设置到默认值吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            alertEnabled: true,
            soundEnabled: true,
            vibrationEnabled: false,
            selectedDelayIndex: 1,
            selectedRepeatIndex: 2
          })
          
          this.initParamSettings()
          
          wx.showToast({
            title: '已恢复默认设置',
            icon: 'success'
          })
        }
      }
    })
  }
})
