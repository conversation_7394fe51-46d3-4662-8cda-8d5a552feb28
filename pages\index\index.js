// pages/index/index.js
const mockData = require('../../utils/mockData.js')

Page({
  data: {
    currentTankType: 'freshwater',
    currentTankName: '淡水缸',
    tankParams: {},
    overviewData: [],
    systemStatus: {
      overall: 'normal',
      text: '正常'
    },
    lastUpdateTime: '',
    refreshing: false,
    recentAlerts: []
  },

  onLoad() {
    this.initData()
    this.loadOverviewData()
    this.loadRecentAlerts()
    
    // 设置定时刷新
    this.refreshTimer = setInterval(() => {
      this.loadOverviewData()
    }, 30000) // 30秒刷新一次
  },

  onUnload() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadOverviewData()
  },

  initData() {
    const app = getApp()
    const currentType = app.globalData.currentTankType
    const tankTypes = app.globalData.tankTypes
    
    this.setData({
      currentTankType: currentType,
      currentTankName: tankTypes[currentType].name,
      tankParams: {
        freshwater: tankTypes.freshwater.params,
        planted: tankTypes.planted.params,
        turtle: tankTypes.turtle.params
      }
    })
  },

  selectTankType(e) {
    const type = e.currentTarget.dataset.type
    const app = getApp()
    const tankTypes = app.globalData.tankTypes
    
    app.globalData.currentTankType = type
    
    this.setData({
      currentTankType: type,
      currentTankName: tankTypes[type].name
    })
    
    // 切换类型后重新加载数据
    this.loadOverviewData()
    this.loadRecentAlerts()
  },

  loadOverviewData() {
    const app = getApp()
    const realtimeData = mockData.generateRealtimeData(this.data.currentTankType)
    const paramConfig = app.globalData.paramConfig
    const overviewData = []
    
    let normalCount = 0
    let warningCount = 0
    let dangerCount = 0
    
    Object.keys(realtimeData).forEach(key => {
      const data = realtimeData[key]
      const config = paramConfig[key]
      
      let statusText = '正常'
      switch(data.status) {
        case 'warning':
          statusText = '警告'
          warningCount++
          break
        case 'danger':
          statusText = '危险'
          dangerCount++
          break
        default:
          normalCount++
      }
      
      overviewData.push({
        key: key,
        name: config.name,
        icon: config.icon,
        value: data.value,
        unit: config.unit,
        status: data.status,
        statusText: statusText
      })
    })
    
    // 确定系统整体状态
    let systemStatus = { overall: 'normal', text: '正常' }
    if (dangerCount > 0) {
      systemStatus = { overall: 'danger', text: '异常' }
    } else if (warningCount > 0) {
      systemStatus = { overall: 'warning', text: '警告' }
    }
    
    this.setData({
      overviewData: overviewData,
      systemStatus: systemStatus,
      lastUpdateTime: new Date().toLocaleTimeString(),
      refreshing: false
    })
  },

  loadRecentAlerts() {
    const alerts = mockData.generateAlerts(this.data.currentTankType, 7)
    // 只显示最近3条预警
    const recentAlerts = alerts.slice(0, 3).map(alert => ({
      ...alert,
      time: this.formatTime(alert.timestamp)
    }))
    
    this.setData({
      recentAlerts: recentAlerts
    })
  },

  refreshData() {
    this.setData({ refreshing: true })
    
    setTimeout(() => {
      this.loadOverviewData()
      this.loadRecentAlerts()
      
      wx.showToast({
        title: '数据已更新',
        icon: 'success',
        duration: 1500
      })
    }, 1000)
  },

  formatTime(timestamp) {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    
    return new Date(timestamp).toLocaleDateString()
  },

  // 页面跳转
  goToMonitor() {
    wx.switchTab({
      url: '/pages/monitor/monitor'
    })
  },

  goToHistory() {
    wx.switchTab({
      url: '/pages/history/history'
    })
  },

  goToSettings() {
    wx.switchTab({
      url: '/pages/settings/settings'
    })
  },

  viewAllAlerts() {
    wx.navigateTo({
      url: '/pages/alerts/alerts'
    })
  },

  exportData() {
    wx.showModal({
      title: '导出数据',
      content: '是否导出最近7天的监控数据？',
      success: (res) => {
        if (res.confirm) {
          // 模拟导出过程
          wx.showLoading({
            title: '正在导出...'
          })
          
          setTimeout(() => {
            wx.hideLoading()
            wx.showToast({
              title: '导出成功',
              icon: 'success'
            })
          }, 2000)
        }
      }
    })
  }
})
