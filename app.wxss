/**app.wxss**/
/* 全局样式重置 */
page {
  background-color: #f7f8fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  color: #323233;
  line-height: 1.4;
}

/* 通用容器 */
.container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 32rpx 40rpx;
  color: white;
}

.page-title {
  font-size: 44rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.page-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 24rpx;
}

/* 按钮样式 */
.btn {
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  padding: 24rpx 48rpx;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.btn-secondary {
  background: #f2f3f5;
  color: #646566;
}

.btn-secondary:active {
  background: #e8e9eb;
}

/* 状态指示器 */
.status-normal { background: #07c160; }
.status-warning { background: #ff976a; }
.status-danger { background: #ee0a24; }

/* 数据展示 */
.data-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #ebedf0;
}

.data-item:last-child {
  border-bottom: none;
}

.data-label {
  font-size: 28rpx;
  color: #646566;
}

.data-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

/* 标签 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.tag-success {
  background: #f0f9ff;
  color: #07c160;
  border: 1rpx solid #c9e9d0;
}

.tag-warning {
  background: #fff7e6;
  color: #ff976a;
  border: 1rpx solid #ffd591;
}

.tag-danger {
  background: #fff1f0;
  color: #ee0a24;
  border: 1rpx solid #ffa39e;
}

/* 全局样式 */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  padding: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #dee2e6;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.status-normal {
  background-color: #28a745;
}

.status-warning {
  background-color: #ffc107;
}

.status-danger {
  background-color: #dc3545;
}

/* 参数显示 */
.param-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.param-item:last-child {
  border-bottom: none;
}

.param-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.param-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.param-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #4A90E2;
}

.param-unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 20rpx;
}

.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

/* 文本样式 */
.text-primary {
  color: #4A90E2;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-muted {
  color: #6c757d;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 间距 */
.mt-10 { margin-top: 20rpx; }
.mt-20 { margin-top: 40rpx; }
.mb-10 { margin-bottom: 20rpx; }
.mb-20 { margin-bottom: 40rpx; }
.ml-10 { margin-left: 20rpx; }
.mr-10 { margin-right: 20rpx; }

.p-10 { padding: 20rpx; }
.p-20 { padding: 40rpx; }
.px-10 { padding-left: 20rpx; padding-right: 20rpx; }
.py-10 { padding-top: 20rpx; padding-bottom: 20rpx; }
