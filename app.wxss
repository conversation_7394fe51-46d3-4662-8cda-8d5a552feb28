/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 全局样式 */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  padding: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #dee2e6;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.status-normal {
  background-color: #28a745;
}

.status-warning {
  background-color: #ffc107;
}

.status-danger {
  background-color: #dc3545;
}

/* 参数显示 */
.param-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.param-item:last-child {
  border-bottom: none;
}

.param-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.param-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.param-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #4A90E2;
}

.param-unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 20rpx;
}

.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

/* 文本样式 */
.text-primary {
  color: #4A90E2;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-muted {
  color: #6c757d;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 间距 */
.mt-10 { margin-top: 20rpx; }
.mt-20 { margin-top: 40rpx; }
.mb-10 { margin-bottom: 20rpx; }
.mb-20 { margin-bottom: 40rpx; }
.ml-10 { margin-left: 20rpx; }
.mr-10 { margin-right: 20rpx; }

.p-10 { padding: 20rpx; }
.p-20 { padding: 40rpx; }
.px-10 { padding-left: 20rpx; padding-right: 20rpx; }
.py-10 { padding-top: 20rpx; padding-bottom: 20rpx; }
