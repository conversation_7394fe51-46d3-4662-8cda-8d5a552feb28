<!--pages/alerts/alerts.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header-section">
    <view class="page-title">预警中心</view>
    <view class="filter-btn" bindtap="showFilterModal">
      <text class="filter-icon">🔍</text>
      <text>筛选</text>
    </view>
  </view>

  <!-- 统计概览 -->
  <view class="stats-overview">
    <view class="stat-card stat-danger">
      <view class="stat-number">{{stats.danger}}</view>
      <view class="stat-label">危险预警</view>
    </view>
    <view class="stat-card stat-warning">
      <view class="stat-number">{{stats.warning}}</view>
      <view class="stat-label">警告预警</view>
    </view>
    <view class="stat-card stat-resolved">
      <view class="stat-number">{{stats.resolved}}</view>
      <view class="stat-label">已解决</view>
    </view>
  </view>

  <!-- 预警列表 -->
  <view class="alerts-section">
    <view class="section-header">
      <view class="section-title">预警列表</view>
      <view class="sort-controls">
        <view class="sort-btn {{sortBy === 'time' ? 'active' : ''}}" bindtap="sortByTime">
          时间
        </view>
        <view class="sort-btn {{sortBy === 'level' ? 'active' : ''}}" bindtap="sortByLevel">
          级别
        </view>
      </view>
    </view>

    <scroll-view class="alerts-list" scroll-y="true" style="height: calc(100vh - 400rpx);">
      <view 
        class="alert-item alert-{{item.level}}"
        wx:for="{{filteredAlerts}}"
        wx:key="id"
      >
        <view class="alert-header">
          <view class="alert-level-badge level-{{item.level}}">
            {{item.level === 'danger' ? '危险' : '警告'}}
          </view>
          <view class="alert-time">{{item.time}}</view>
        </view>
        
        <view class="alert-content">
          <view class="alert-message">{{item.message}}</view>
          <view class="alert-param">
            <text class="param-icon">{{item.paramIcon}}</text>
            <text class="param-name">{{item.parameterName}}</text>
          </view>
        </view>
        
        <view class="alert-actions">
          <view 
            class="action-btn resolve-btn {{item.resolved ? 'resolved' : ''}}"
            bindtap="toggleResolve"
            data-id="{{item.id}}"
          >
            {{item.resolved ? '已解决' : '标记解决'}}
          </view>
          <view class="action-btn detail-btn" bindtap="showDetail" data-alert="{{item}}">
            详情
          </view>
        </view>
      </view>
      
      <view class="no-alerts" wx:if="{{filteredAlerts.length === 0}}">
        <text class="no-alerts-icon">🎉</text>
        <text class="no-alerts-text">暂无预警</text>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 筛选弹窗 -->
<view class="filter-modal {{showFilter ? 'show' : ''}}" bindtap="hideFilterModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">筛选条件</view>
      <view class="close-btn" bindtap="hideFilterModal">×</view>
    </view>
    
    <view class="modal-body">
      <view class="filter-group">
        <view class="filter-label">预警级别</view>
        <view class="filter-options">
          <view 
            class="filter-option {{levelFilter === 'all' ? 'active' : ''}}"
            bindtap="setLevelFilter"
            data-level="all"
          >
            全部
          </view>
          <view 
            class="filter-option {{levelFilter === 'danger' ? 'active' : ''}}"
            bindtap="setLevelFilter"
            data-level="danger"
          >
            危险
          </view>
          <view 
            class="filter-option {{levelFilter === 'warning' ? 'active' : ''}}"
            bindtap="setLevelFilter"
            data-level="warning"
          >
            警告
          </view>
        </view>
      </view>
      
      <view class="filter-group">
        <view class="filter-label">解决状态</view>
        <view class="filter-options">
          <view 
            class="filter-option {{statusFilter === 'all' ? 'active' : ''}}"
            bindtap="setStatusFilter"
            data-status="all"
          >
            全部
          </view>
          <view 
            class="filter-option {{statusFilter === 'unresolved' ? 'active' : ''}}"
            bindtap="setStatusFilter"
            data-status="unresolved"
          >
            未解决
          </view>
          <view 
            class="filter-option {{statusFilter === 'resolved' ? 'active' : ''}}"
            bindtap="setStatusFilter"
            data-status="resolved"
          >
            已解决
          </view>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="btn-secondary" bindtap="resetFilter">重置</button>
      <button class="btn-primary" bindtap="applyFilter">应用</button>
    </view>
  </view>
</view>

<!-- 详情弹窗 -->
<view class="detail-modal {{showDetail ? 'show' : ''}}" bindtap="hideDetailModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">预警详情</view>
      <view class="close-btn" bindtap="hideDetailModal">×</view>
    </view>
    
    <view class="modal-body">
      <view class="detail-info">
        <view class="detail-item">
          <view class="detail-label">预警级别</view>
          <view class="detail-value level-{{selectedAlert.level}}">
            {{selectedAlert.level === 'danger' ? '危险' : '警告'}}
          </view>
        </view>
        
        <view class="detail-item">
          <view class="detail-label">参数名称</view>
          <view class="detail-value">
            <text class="param-icon">{{selectedAlert.paramIcon}}</text>
            <text>{{selectedAlert.parameterName}}</text>
          </view>
        </view>
        
        <view class="detail-item">
          <view class="detail-label">预警消息</view>
          <view class="detail-value">{{selectedAlert.message}}</view>
        </view>
        
        <view class="detail-item">
          <view class="detail-label">发生时间</view>
          <view class="detail-value">{{selectedAlert.date}} {{selectedAlert.time}}</view>
        </view>
        
        <view class="detail-item">
          <view class="detail-label">解决状态</view>
          <view class="detail-value status-{{selectedAlert.resolved ? 'resolved' : 'pending'}}">
            {{selectedAlert.resolved ? '已解决' : '待处理'}}
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
