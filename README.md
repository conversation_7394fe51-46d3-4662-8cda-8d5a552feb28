# 智慧水族远程监控系统 - 微信小程序前端

基于ESP8266的智慧水族远程监控系统的微信小程序前端实现，支持淡水缸、草缸、龟缸三种水族箱类型的环境监控。

## 项目特色

### 🐠 多类型水族箱支持
- **淡水缸**: 监控水温、pH值、溶氧量、浊度、水位
- **草缸**: 监控水温、pH值、CO₂浓度、光照强度、营养液、水位  
- **龟缸**: 监控水温、湿度、UVB强度、水位、晒台温度

### 📊 实时监控功能
- 实时环境参数显示
- 参数状态指示（正常/警告/危险）
- 自动/手动数据刷新
- 参数趋势分析
- 进度条可视化显示

### 📈 历史数据分析
- 多时间段数据查询（今日/昨日/本周/本月）
- 交互式图表展示
- 统计信息汇总（平均值/最大值/最小值/正常率）
- 数据导出功能

### 🔔 智能预警系统
- 可自定义参数阈值
- 多级预警（警告/危险）
- 预警历史记录
- 声音和震动提醒
- 预警状态管理

### 🎨 现代化UI设计
- 渐变色彩搭配
- 卡片式布局
- 响应式设计
- 流畅动画效果
- 直观的数据可视化

## 技术架构

### 前端技术栈
- **框架**: 微信小程序原生开发
- **样式**: WXSS (支持Flexbox、Grid布局)
- **数据**: 模拟数据生成 + 本地存储
- **图表**: 自定义Canvas图表组件

### 项目结构
```
├── app.js                 # 小程序入口文件
├── app.json              # 全局配置
├── app.wxss              # 全局样式
├── pages/                # 页面目录
│   ├── index/            # 首页
│   ├── monitor/          # 实时监控
│   ├── history/          # 历史数据
│   ├── settings/         # 预警设置
│   └── alerts/           # 预警中心
├── utils/                # 工具类
│   └── mockData.js       # 模拟数据生成
└── images/               # 图标资源
```

## 功能模块

### 1. 首页 (pages/index)
- 水族箱类型选择
- 系统状态概览
- 环境参数快览
- 快速操作入口
- 最近预警提醒

### 2. 实时监控 (pages/monitor)
- 实时参数显示
- 参数详情弹窗
- 自动刷新控制
- 快速操作面板
- 系统运行信息

### 3. 历史数据 (pages/history)
- 时间范围筛选
- 参数类型选择
- 趋势图表展示
- 统计信息汇总
- 数据列表查看

### 4. 预警设置 (pages/settings)
- 预警功能开关
- 参数阈值设置
- 通知方式配置
- 预警历史管理
- 设置导入导出

### 5. 预警中心 (pages/alerts)
- 预警统计概览
- 预警列表管理
- 筛选和排序
- 预警详情查看
- 状态标记功能

## 数据模拟

### 模拟数据特点
- **真实性**: 基于实际水族箱参数范围
- **时间相关**: 模拟日夜变化和季节影响
- **状态变化**: 动态生成正常/警告/危险状态
- **趋势分析**: 支持上升/下降/稳定趋势
- **历史数据**: 可生成任意时间段的历史记录

### 参数配置
每个参数包含以下配置：
- 名称和图标
- 单位和数值范围
- 适宜范围定义
- 预警阈值设置
- 数据生成规则

## 安装和运行

### 环境要求
- 微信开发者工具
- 微信小程序开发账号

### 运行步骤
1. 下载项目代码
2. 使用微信开发者工具打开项目
3. 添加必要的图标文件到 `images/` 目录
4. 编译并预览小程序

### 图标文件
需要在 `images/` 目录下添加以下图标：
- home.png / home-active.png
- monitor.png / monitor-active.png  
- history.png / history-active.png
- settings.png / settings-active.png

## 自定义配置

### 添加新的水族箱类型
在 `app.js` 的 `globalData.tankTypes` 中添加新类型：

```javascript
newTankType: {
  name: '新类型',
  icon: '🐟',
  color: '#FF6B6B',
  params: ['temperature', 'ph', 'customParam']
}
```

### 添加新的监控参数
在 `app.js` 的 `globalData.paramConfig` 中添加新参数：

```javascript
customParam: {
  name: '自定义参数',
  unit: 'unit',
  icon: '📊',
  min: 0,
  max: 100,
  optimal: [30, 70]
}
```

### 修改模拟数据规则
在 `utils/mockData.js` 中的 `generateRealtimeData` 函数中添加自定义逻辑。

## 扩展功能

### 可扩展的功能点
- 设备控制功能（加热器、过滤器、灯光等）
- 用户账号系统
- 多设备管理
- 数据云端同步
- 社区分享功能
- AI智能建议

### 与硬件集成
- 替换模拟数据为真实API调用
- 添加WebSocket实时通信
- 集成OneNet物联网平台
- 支持设备离线检测

## 技术亮点

1. **模块化设计**: 清晰的代码结构，易于维护和扩展
2. **响应式布局**: 适配不同屏幕尺寸的设备
3. **性能优化**: 合理的数据更新策略和内存管理
4. **用户体验**: 流畅的交互动画和直观的界面设计
5. **数据可视化**: 自定义图表组件，支持多种展示方式

## 许可证

本项目仅供学习和演示使用。

## 联系方式

如有问题或建议，欢迎提出Issue或Pull Request。
