// pages/history/history.js
const mockData = require('../../utils/mockData.js')

Page({
  data: {
    timePeriods: [
      { label: '今日', value: 'today' },
      { label: '昨日', value: 'yesterday' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
      { label: '自定义', value: 'custom' }
    ],
    selectedPeriod: 'today',
    selectedPeriodText: '今日',
    availableParams: [],
    selectedParam: 'temperature',
    currentParamConfig: {},
    historyData: [],
    chartData: [],
    statistics: {},
    sortOrder: 'desc',
    hasMore: true,
    loading: false,
    showDataModal: false,
    selectedDataPoint: {},
    trendLineStyle: ''
  },

  onLoad() {
    this.initParams()
    this.loadHistoryData()
  },

  onShow() {
    // 检查是否需要更新水族箱类型
    this.initParams()
    this.loadHistoryData()
  },

  initParams() {
    const app = getApp()
    const currentType = app.globalData.currentTankType
    const tankConfig = app.globalData.tankTypes[currentType]
    const paramConfig = app.globalData.paramConfig
    
    const availableParams = tankConfig.params.map(paramKey => ({
      key: paramKey,
      name: paramConfig[paramKey].name,
      icon: paramConfig[paramKey].icon
    }))
    
    // 如果当前选择的参数不在可用参数中，选择第一个
    if (!tankConfig.params.includes(this.data.selectedParam)) {
      this.setData({
        selectedParam: tankConfig.params[0]
      })
    }
    
    this.setData({
      availableParams: availableParams,
      currentParamConfig: paramConfig[this.data.selectedParam]
    })
  },

  selectPeriod(e) {
    const period = e.currentTarget.dataset.period
    const periodTexts = {
      'today': '今日',
      'yesterday': '昨日',
      'week': '本周',
      'month': '本月',
      'custom': '自定义'
    }
    
    this.setData({
      selectedPeriod: period,
      selectedPeriodText: periodTexts[period]
    })
    
    this.loadHistoryData()
  },

  selectParam(e) {
    const param = e.currentTarget.dataset.param
    const app = getApp()
    const paramConfig = app.globalData.paramConfig
    
    this.setData({
      selectedParam: param,
      currentParamConfig: paramConfig[param]
    })
    
    this.loadHistoryData()
  },

  loadHistoryData() {
    wx.showLoading({
      title: '加载中...'
    })
    
    // 根据选择的时间段确定天数
    let days = 1
    switch(this.data.selectedPeriod) {
      case 'today':
      case 'yesterday':
        days = 1
        break
      case 'week':
        days = 7
        break
      case 'month':
        days = 30
        break
    }
    
    setTimeout(() => {
      const app = getApp()
      const currentType = app.globalData.currentTankType
      const historyData = mockData.generateHistoryData(currentType, days)
      
      // 处理历史数据，添加状态文本
      const processedData = historyData.map(item => ({
        ...item,
        statusText: this.getStatusText(item.data[this.data.selectedParam].status)
      }))
      
      // 排序
      if (this.data.sortOrder === 'desc') {
        processedData.sort((a, b) => b.timestamp - a.timestamp)
      } else {
        processedData.sort((a, b) => a.timestamp - b.timestamp)
      }
      
      // 生成图表数据
      const chartData = this.generateChartData(processedData)
      
      // 计算统计信息
      const statistics = this.calculateStatistics(processedData)
      
      this.setData({
        historyData: processedData.slice(0, 50), // 只显示前50条
        chartData: chartData,
        statistics: statistics,
        hasMore: processedData.length > 50
      })
      
      wx.hideLoading()
    }, 1000)
  },

  generateChartData(data) {
    const chartData = []
    const paramConfig = this.data.currentParamConfig
    
    // 取样数据点（最多50个点）
    const sampleSize = Math.min(50, data.length)
    const step = Math.max(1, Math.floor(data.length / sampleSize))
    
    for (let i = 0; i < data.length; i += step) {
      const item = data[i]
      const value = item.data[this.data.selectedParam].value
      const status = item.data[this.data.selectedParam].status
      
      // 计算位置百分比
      const x = (i / (data.length - 1)) * 100
      const y = ((value - paramConfig.min) / (paramConfig.max - paramConfig.min)) * 100
      
      // 确定颜色
      let color = '#28a745'
      if (status === 'warning') color = '#ffc107'
      else if (status === 'danger') color = '#dc3545'
      
      chartData.push({
        x: x,
        y: Math.max(0, Math.min(100, y)),
        value: value,
        color: color,
        timestamp: item.timestamp,
        date: item.date,
        time: item.time,
        status: status
      })
    }
    
    return chartData
  },

  calculateStatistics(data) {
    if (data.length === 0) {
      return {
        average: 0,
        max: 0,
        min: 0,
        normalRate: 0
      }
    }
    
    const values = data.map(item => item.data[this.data.selectedParam].value)
    const statuses = data.map(item => item.data[this.data.selectedParam].status)
    
    const sum = values.reduce((a, b) => a + b, 0)
    const average = (sum / values.length).toFixed(1)
    const max = Math.max(...values)
    const min = Math.min(...values)
    
    const normalCount = statuses.filter(status => status === 'normal').length
    const normalRate = Math.round((normalCount / statuses.length) * 100)
    
    return {
      average: average,
      max: max,
      min: min,
      normalRate: normalRate
    }
  },

  getStatusText(status) {
    switch(status) {
      case 'normal': return '正常'
      case 'warning': return '警告'
      case 'danger': return '危险'
      default: return '未知'
    }
  },

  toggleSort() {
    const newOrder = this.data.sortOrder === 'desc' ? 'asc' : 'desc'
    const sortedData = [...this.data.historyData]
    
    if (newOrder === 'desc') {
      sortedData.sort((a, b) => b.timestamp - a.timestamp)
    } else {
      sortedData.sort((a, b) => a.timestamp - b.timestamp)
    }
    
    this.setData({
      sortOrder: newOrder,
      historyData: sortedData
    })
  },

  loadMore() {
    if (this.data.loading || !this.data.hasMore) return
    
    this.setData({ loading: true })
    
    setTimeout(() => {
      // 模拟加载更多数据
      const moreData = []
      for (let i = 0; i < 20; i++) {
        moreData.push({
          timestamp: Date.now() - Math.random() * 86400000,
          date: new Date().toLocaleDateString(),
          time: new Date().toLocaleTimeString(),
          data: {
            [this.data.selectedParam]: {
              value: Math.random() * 30 + 20,
              status: 'normal'
            }
          },
          statusText: '正常'
        })
      }
      
      this.setData({
        historyData: [...this.data.historyData, ...moreData],
        loading: false,
        hasMore: this.data.historyData.length < 200 // 最多200条
      })
    }, 1000)
  },

  showDataPoint(e) {
    const index = e.currentTarget.dataset.index
    const dataPoint = this.data.chartData[index]
    
    if (dataPoint) {
      this.setData({
        selectedDataPoint: {
          ...dataPoint,
          statusText: this.getStatusText(dataPoint.status)
        },
        showDataModal: true
      })
    }
  },

  hideDataModal() {
    this.setData({ showDataModal: false })
  },

  stopPropagation() {
    // 阻止事件冒泡
  },

  exportData() {
    wx.showActionSheet({
      itemList: ['导出为Excel', '导出为CSV', '导出为PDF'],
      success: (res) => {
        const formats = ['Excel', 'CSV', 'PDF']
        wx.showLoading({
          title: `正在导出${formats[res.tapIndex]}...`
        })
        
        setTimeout(() => {
          wx.hideLoading()
          wx.showToast({
            title: '导出成功',
            icon: 'success'
          })
        }, 2000)
      }
    })
  }
})
