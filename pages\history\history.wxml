<!--pages/history/history.wxml-->
<view class="container">
  <view class="filter-section">
    <view class="filter-header">
      <view class="page-title">历史数据</view>
      <view class="export-btn" bindtap="exportData">
        <text class="export-icon">📤</text>
        <text>导出</text>
      </view>
    </view>
    
    <view class="filter-controls">
      <view class="time-filter">
        <view class="filter-label">时间范围</view>
        <scroll-view class="time-tabs" scroll-x="true">
          <view 
            class="time-tab {{selectedPeriod === item.value ? 'active' : ''}}"
            wx:for="{{timePeriods}}"
            wx:key="value"
            bindtap="selectPeriod"
            data-period="{{item.value}}"
          >
            {{item.label}}
          </view>
        </scroll-view>
      </view>
      
      <view class="param-filter">
        <view class="filter-label">监控参数</view>
        <scroll-view class="param-tabs" scroll-x="true">
          <view 
            class="param-tab {{selectedParam === item.key ? 'active' : ''}}"
            wx:for="{{availableParams}}"
            wx:key="key"
            bindtap="selectParam"
            data-param="{{item.key}}"
          >
            <text class="param-icon">{{item.icon}}</text>
            <text class="param-name">{{item.name}}</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>

  <view class="chart-section">
    <view class="chart-header">
      <view class="chart-title">
        <text class="param-icon">{{currentParamConfig.icon}}</text>
        <text>{{currentParamConfig.name}}趋势图</text>
      </view>
      <view class="chart-info">
        <text class="period-text">{{selectedPeriodText}}</text>
      </view>
    </view>
    
    <view class="chart-container">
      <view class="chart-canvas">
        <view class="chart-grid">
          <view class="grid-line" wx:for="{{5}}" wx:key="*this"></view>
        </view>
        <view class="chart-data">
          <view 
            class="data-point"
            wx:for="{{chartData}}"
            wx:key="index"
            style="left: {{item.x}}%; bottom: {{item.y}}%; background-color: {{item.color}}"
            bindtap="showDataPoint"
            data-index="{{index}}"
          ></view>
          <view class="trend-line" style="{{trendLineStyle}}"></view>
        </view>
      </view>
      
      <view class="chart-legend">
        <view class="legend-item">
          <view class="legend-color normal"></view>
          <text>正常范围</text>
        </view>
        <view class="legend-item">
          <view class="legend-color warning"></view>
          <text>警告范围</text>
        </view>
        <view class="legend-item">
          <view class="legend-color danger"></view>
          <text>异常范围</text>
        </view>
      </view>
    </view>
  </view>

  <view class="stats-section">
    <view class="stats-header">
      <view class="stats-title">统计信息</view>
      <view class="stats-period">{{selectedPeriodText}}</view>
    </view>
    
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-value">{{statistics.average}}</view>
        <view class="stat-label">平均值</view>
        <view class="stat-unit">{{currentParamConfig.unit}}</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{statistics.max}}</view>
        <view class="stat-label">最大值</view>
        <view class="stat-unit">{{currentParamConfig.unit}}</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{statistics.min}}</view>
        <view class="stat-label">最小值</view>
        <view class="stat-unit">{{currentParamConfig.unit}}</view>
      </view>
      <view class="stat-item">
        <view class="stat-value">{{statistics.normalRate}}%</view>
        <view class="stat-label">正常率</view>
        <view class="stat-unit"></view>
      </view>
    </view>
  </view>

  <view class="data-list-section">
    <view class="list-header">
      <view class="list-title">详细数据</view>
      <view class="list-controls">
        <view class="sort-btn {{sortOrder === 'desc' ? 'active' : ''}}" bindtap="toggleSort">
          <text class="sort-icon">{{sortOrder === 'desc' ? '↓' : '↑'}}</text>
          <text>时间</text>
        </view>
      </view>
    </view>
    
    <scroll-view class="data-list" scroll-y="true" style="height: 400rpx;">
      <view 
        class="data-item"
        wx:for="{{historyData}}"
        wx:key="timestamp"
      >
        <view class="data-time">
          <view class="date">{{item.date}}</view>
          <view class="time">{{item.time}}</view>
        </view>
        <view class="data-value">
          <view class="value-display">
            <text class="value">{{item.data[selectedParam].value}}</text>
            <text class="unit">{{currentParamConfig.unit}}</text>
          </view>
          <view class="value-status">
            <view class="status-indicator status-{{item.data[selectedParam].status}}"></view>
            <text class="status-text">{{item.statusText}}</text>
          </view>
        </view>
      </view>
      
      <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMore">
        <text class="load-text">{{loading ? '加载中...' : '加载更多'}}</text>
      </view>
      
      <view class="no-more" wx:if="{{!hasMore && historyData.length > 0}}">
        <text>没有更多数据了</text>
      </view>
    </scroll-view>
  </view>
</view>

<view class="data-modal {{showDataModal ? 'show' : ''}}" bindtap="hideDataModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">数据详情</view>
      <view class="close-btn" bindtap="hideDataModal">×</view>
    </view>
    
    <view class="modal-body">
      <view class="data-detail">
        <view class="detail-time">
          <view class="detail-date">{{selectedDataPoint.date}}</view>
          <view class="detail-time-text">{{selectedDataPoint.time}}</view>
        </view>
        
        <view class="detail-value">
          <view class="param-info">
            <text class="param-icon">{{currentParamConfig.icon}}</text>
            <text class="param-name">{{currentParamConfig.name}}</text>
          </view>
          <view class="value-info">
            <text class="value">{{selectedDataPoint.value}}</text>
            <text class="unit">{{currentParamConfig.unit}}</text>
          </view>
          <view class="status-info">
            <view class="status-indicator status-{{selectedDataPoint.status}}"></view>
            <text class="status-text">{{selectedDataPoint.statusText}}</text>
          </view>
        </view>
        
        <view class="detail-range">
          <view class="range-title">参考范围</view>
          <view class="range-info">
            <view class="range-item">
              <text class="range-label">适宜范围:</text>
              <text class="range-value">{{currentParamConfig.optimal[0]}}-{{currentParamConfig.optimal[1]}}{{currentParamConfig.unit}}</text>
            </view>
            <view class="range-item">
              <text class="range-label">监控范围:</text>
              <text class="range-value">{{currentParamConfig.min}}-{{currentParamConfig.max}}{{currentParamConfig.unit}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
