/* pages/test/test.wxss */
.container {
  background: #f7f8fa;
  min-height: 100vh;
}

.content text {
  display: block;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #646566;
  line-height: 1.5;
}

.feature-list {
  margin-top: 24rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #ebedf0;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  width: 48rpx;
  text-align: center;
}

.feature-text {
  font-size: 28rpx;
  color: #323233;
  flex: 1;
}

.action-buttons {
  padding: 40rpx 32rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 500;
}
