<!--pages/index/index.wxml-->
<view class="container">
  <!-- 头部欢迎区域 -->
  <view class="header-section">
    <view class="welcome-card card">
      <view class="welcome-content">
        <view class="welcome-title">智慧水族监控系统</view>
        <view class="welcome-subtitle">实时监控您的水族箱环境</view>
        <view class="system-status">
          <view class="status-item">
            <view class="status-indicator status-{{systemStatus.overall}}"></view>
            <text class="status-text">系统{{systemStatus.text}}</text>
          </view>
          <view class="last-update">
            最后更新: {{lastUpdateTime}}
          </view>
        </view>
      </view>
      <view class="welcome-icon">🐠</view>
    </view>
  </view>

  <!-- 水族箱类型选择 -->
  <view class="tank-selection">
    <view class="section-title">选择水族箱类型</view>
    <view class="tank-grid">
      <view 
        class="tank-card {{currentTankType === 'freshwater' ? 'active' : ''}}"
        bindtap="selectTankType"
        data-type="freshwater"
      >
        <view class="tank-icon">🐠</view>
        <view class="tank-name">淡水缸</view>
        <view class="tank-desc">观赏鱼养殖</view>
        <view class="param-count">{{tankParams.freshwater.length}}项参数</view>
      </view>
      
      <view 
        class="tank-card {{currentTankType === 'planted' ? 'active' : ''}}"
        bindtap="selectTankType"
        data-type="planted"
      >
        <view class="tank-icon">🌱</view>
        <view class="tank-name">草缸</view>
        <view class="tank-desc">水草造景</view>
        <view class="param-count">{{tankParams.planted.length}}项参数</view>
      </view>
      
      <view 
        class="tank-card {{currentTankType === 'turtle' ? 'active' : ''}}"
        bindtap="selectTankType"
        data-type="turtle"
      >
        <view class="tank-icon">🐢</view>
        <view class="tank-name">龟缸</view>
        <view class="tank-desc">爬虫饲养</view>
        <view class="param-count">{{tankParams.turtle.length}}项参数</view>
      </view>
    </view>
  </view>

  <!-- 当前环境概览 -->
  <view class="overview-section">
    <view class="section-title">
      <text>{{currentTankName}}环境概览</text>
      <view class="refresh-btn" bindtap="refreshData">
        <text class="refresh-icon {{refreshing ? 'rotating' : ''}}">🔄</text>
      </view>
    </view>
    
    <view class="overview-grid">
      <view 
        class="overview-item" 
        wx:for="{{overviewData}}" 
        wx:key="key"
      >
        <view class="param-header">
          <text class="param-icon">{{item.icon}}</text>
          <text class="param-name">{{item.name}}</text>
        </view>
        <view class="param-value-section">
          <text class="param-value">{{item.value}}</text>
          <text class="param-unit">{{item.unit}}</text>
        </view>
        <view class="param-status">
          <view class="status-indicator status-{{item.status}}"></view>
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <view class="section-title">快速操作</view>
    <view class="action-grid">
      <view class="action-item" bindtap="goToMonitor">
        <view class="action-icon">📊</view>
        <view class="action-name">实时监控</view>
      </view>
      <view class="action-item" bindtap="goToHistory">
        <view class="action-icon">📈</view>
        <view class="action-name">历史数据</view>
      </view>
      <view class="action-item" bindtap="goToSettings">
        <view class="action-icon">⚙️</view>
        <view class="action-name">预警设置</view>
      </view>
      <view class="action-item" bindtap="exportData">
        <view class="action-icon">📤</view>
        <view class="action-name">导出数据</view>
      </view>
    </view>
  </view>

  <!-- 最近预警 -->
  <view class="recent-alerts" wx:if="{{recentAlerts.length > 0}}">
    <view class="section-title">最近预警</view>
    <view class="alert-list">
      <view 
        class="alert-item alert-{{item.level}}" 
        wx:for="{{recentAlerts}}" 
        wx:key="id"
      >
        <view class="alert-content">
          <view class="alert-message">{{item.message}}</view>
          <view class="alert-time">{{item.time}}</view>
        </view>
        <view class="alert-status">
          <text wx:if="{{item.resolved}}" class="resolved">已解决</text>
          <text wx:else class="pending">待处理</text>
        </view>
      </view>
    </view>
    <view class="view-all-alerts" bindtap="viewAllAlerts">
      查看全部预警 →
    </view>
  </view>
</view>
