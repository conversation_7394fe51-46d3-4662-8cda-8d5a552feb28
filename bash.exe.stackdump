Stack trace:
Frame         Function      Args
0007FFFFAC10  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFAC10, 0007FFFF9B10) msys-2.0.dll+0x1FE8E
0007FFFFAC10  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAEE8) msys-2.0.dll+0x67F9
0007FFFFAC10  000210046832 (000210286019, 0007FFFFAAC8, 0007FFFFAC10, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC10  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAC10  000210068E24 (0007FFFFAC20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAEF0  00021006A225 (0007FFFFAC20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF910A40000 ntdll.dll
7FF90EC60000 KERNEL32.DLL
7FF90E1E0000 KERNELBASE.dll
7FF90E7E0000 USER32.dll
7FF90DC20000 win32u.dll
000210040000 msys-2.0.dll
7FF9101B0000 GDI32.dll
7FF90DF50000 gdi32full.dll
7FF90E730000 msvcp_win.dll
7FF90E090000 ucrtbase.dll
7FF90EAA0000 advapi32.dll
7FF90ED40000 msvcrt.dll
7FF910950000 sechost.dll
7FF90F450000 RPCRT4.dll
7FF90D2B0000 CRYPTBASE.DLL
7FF90E690000 bcryptPrimitives.dll
7FF90EDF0000 IMM32.DLL
