/* pages/history/history.wxss */
.container {
  background: #f8f9fa;
  min-height: 100vh;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
}

.export-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #4A90E2;
  color: white;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.export-icon {
  margin-right: 8rpx;
}

.filter-controls {
  margin-top: 20rpx;
}

.filter-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

/* 时间筛选 */
.time-filter {
  margin-bottom: 30rpx;
}

.time-tabs {
  white-space: nowrap;
}

.time-tab {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 16rpx;
  background: #f8f9fa;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.time-tab.active {
  background: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

/* 参数筛选 */
.param-tabs {
  white-space: nowrap;
}

.param-tab {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 24rpx;
  margin-right: 16rpx;
  background: #f8f9fa;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.param-tab.active {
  background: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

.param-tab .param-icon {
  margin-right: 8rpx;
  font-size: 20rpx;
}

/* 图表区域 */
.chart-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.chart-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.chart-title .param-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.chart-info {
  font-size: 22rpx;
  color: #666;
}

.chart-container {
  position: relative;
}

.chart-canvas {
  height: 300rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  position: relative;
  overflow: hidden;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.grid-line {
  height: 1rpx;
  background: rgba(0, 0, 0, 0.1);
}

.chart-data {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.data-point {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  transform: translate(-50%, 50%);
  transition: all 0.3s ease;
  z-index: 2;
}

.data-point:active {
  transform: translate(-50%, 50%) scale(1.5);
}

.trend-line {
  position: absolute;
  height: 2rpx;
  background: #4A90E2;
  opacity: 0.6;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  margin-top: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #666;
}

.legend-color {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.legend-color.normal {
  background: #28a745;
}

.legend-color.warning {
  background: #ffc107;
}

.legend-color.danger {
  background: #dc3545;
}

/* 统计信息 */
.stats-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.stats-period {
  font-size: 22rpx;
  color: #666;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 700;
  color: #4A90E2;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.stat-unit {
  font-size: 20rpx;
  color: #999;
}

/* 数据列表 */
.data-list-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.list-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.sort-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #666;
  transition: all 0.3s ease;
}

.sort-btn.active {
  background: #4A90E2;
  color: white;
}

.sort-icon {
  margin-right: 8rpx;
}

.data-list {
  border-top: 1rpx solid #eee;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.data-item:last-child {
  border-bottom: none;
}

.data-time {
  flex: 1;
}

.date {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.time {
  font-size: 20rpx;
  color: #999;
}

.data-value {
  text-align: right;
}

.value-display {
  margin-bottom: 8rpx;
}

.value {
  font-size: 28rpx;
  font-weight: 600;
  color: #4A90E2;
}

.unit {
  font-size: 20rpx;
  color: #666;
  margin-left: 4rpx;
}

.value-status {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.status-text {
  font-size: 20rpx;
  color: #666;
  margin-left: 8rpx;
}

.load-more, .no-more {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 24rpx;
}

/* 数据点详情弹窗 */
.data-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.data-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  overflow: hidden;
  transform: translateY(50rpx);
  transition: transform 0.3s ease;
}

.data-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  background: #f8f9fa;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
}

.modal-body {
  padding: 30rpx;
}

.data-detail {
  text-align: center;
}

.detail-time {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.detail-date {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.detail-time-text {
  font-size: 24rpx;
  color: #666;
}

.detail-value {
  margin-bottom: 30rpx;
}

.param-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.param-info .param-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.param-info .param-name {
  font-size: 28rpx;
  color: #333;
}

.value-info {
  margin-bottom: 16rpx;
}

.value-info .value {
  font-size: 48rpx;
  font-weight: 700;
  color: #4A90E2;
}

.value-info .unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

.status-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-info .status-text {
  font-size: 24rpx;
  margin-left: 8rpx;
}

.detail-range {
  text-align: left;
}

.range-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.range-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #eee;
}

.range-item:last-child {
  border-bottom: none;
}

.range-label {
  font-size: 24rpx;
  color: #666;
}

.range-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
