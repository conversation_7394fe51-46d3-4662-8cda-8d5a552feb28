<!--pages/test/test.wxml-->
<view class="container">
  <view class="page-header">
    <view class="page-title">UI样式预览</view>
    <view class="page-subtitle">现代化小程序界面设计</view>
  </view>

  <view class="card">
    <view class="card-title">✅ 编译成功</view>
    <view class="content">
      <text>恭喜！小程序已成功编译运行</text>
      <text>新的UI设计采用了现代化的设计规范</text>
    </view>
  </view>

  <view class="card">
    <view class="card-title">🎨 设计特色</view>
    <view class="feature-list">
      <view class="feature-item">
        <text class="feature-icon">🎯</text>
        <text class="feature-text">简洁现代的卡片式设计</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">🌈</text>
        <text class="feature-text">渐变色彩搭配</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">📱</text>
        <text class="feature-text">响应式布局</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">✨</text>
        <text class="feature-text">流畅的交互动效</text>
      </view>
    </view>
  </view>

  <view class="action-buttons">
    <button class="btn btn-primary" bindtap="goToIndex">
      <text class="btn-text">🏠 返回首页</text>
    </button>
  </view>
</view>
