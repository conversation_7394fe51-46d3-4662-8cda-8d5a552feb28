// pages/alerts/alerts.js
const mockData = require('../../utils/mockData.js')

Page({
  data: {
    alerts: [],
    filteredAlerts: [],
    stats: {
      danger: 0,
      warning: 0,
      resolved: 0
    },
    sortBy: 'time',
    showFilter: false,
    levelFilter: 'all',
    statusFilter: 'all',
    showDetail: false,
    selectedAlert: {}
  },

  onLoad() {
    this.loadAlerts()
  },

  onShow() {
    this.loadAlerts()
  },

  loadAlerts() {
    const app = getApp()
    const currentType = app.globalData.currentTankType
    const paramConfig = app.globalData.paramConfig
    const alerts = mockData.generateAlerts(currentType, 30)
    
    // 添加参数图标信息
    const enrichedAlerts = alerts.map(alert => ({
      ...alert,
      paramIcon: paramConfig[alert.parameter]?.icon || '❓'
    }))
    
    // 计算统计信息
    const stats = {
      danger: enrichedAlerts.filter(alert => alert.level === 'danger').length,
      warning: enrichedAlerts.filter(alert => alert.level === 'warning').length,
      resolved: enrichedAlerts.filter(alert => alert.resolved).length
    }
    
    this.setData({
      alerts: enrichedAlerts,
      filteredAlerts: enrichedAlerts,
      stats: stats
    })
  },

  sortByTime() {
    const sorted = [...this.data.filteredAlerts].sort((a, b) => b.timestamp - a.timestamp)
    this.setData({
      filteredAlerts: sorted,
      sortBy: 'time'
    })
  },

  sortByLevel() {
    const levelOrder = { 'danger': 2, 'warning': 1 }
    const sorted = [...this.data.filteredAlerts].sort((a, b) => {
      const levelDiff = levelOrder[b.level] - levelOrder[a.level]
      if (levelDiff !== 0) return levelDiff
      return b.timestamp - a.timestamp
    })
    
    this.setData({
      filteredAlerts: sorted,
      sortBy: 'level'
    })
  },

  toggleResolve(e) {
    const alertId = e.currentTarget.dataset.id
    
    const updatedAlerts = this.data.alerts.map(alert => {
      if (alert.id === alertId) {
        return { ...alert, resolved: !alert.resolved }
      }
      return alert
    })
    
    // 重新计算统计信息
    const stats = {
      danger: updatedAlerts.filter(alert => alert.level === 'danger').length,
      warning: updatedAlerts.filter(alert => alert.level === 'warning').length,
      resolved: updatedAlerts.filter(alert => alert.resolved).length
    }
    
    this.setData({
      alerts: updatedAlerts,
      stats: stats
    })
    
    // 重新应用筛选
    this.applyCurrentFilter()
    
    wx.showToast({
      title: '状态已更新',
      icon: 'success'
    })
  },

  showDetail(e) {
    const alert = e.currentTarget.dataset.alert
    this.setData({
      selectedAlert: alert,
      showDetail: true
    })
  },

  hideDetailModal() {
    this.setData({ showDetail: false })
  },

  showFilterModal() {
    this.setData({ showFilter: true })
  },

  hideFilterModal() {
    this.setData({ showFilter: false })
  },

  setLevelFilter(e) {
    const level = e.currentTarget.dataset.level
    this.setData({ levelFilter: level })
  },

  setStatusFilter(e) {
    const status = e.currentTarget.dataset.status
    this.setData({ statusFilter: status })
  },

  resetFilter() {
    this.setData({
      levelFilter: 'all',
      statusFilter: 'all'
    })
  },

  applyFilter() {
    this.applyCurrentFilter()
    this.hideFilterModal()
  },

  applyCurrentFilter() {
    let filtered = [...this.data.alerts]
    
    // 按级别筛选
    if (this.data.levelFilter !== 'all') {
      filtered = filtered.filter(alert => alert.level === this.data.levelFilter)
    }
    
    // 按状态筛选
    if (this.data.statusFilter !== 'all') {
      if (this.data.statusFilter === 'resolved') {
        filtered = filtered.filter(alert => alert.resolved)
      } else if (this.data.statusFilter === 'unresolved') {
        filtered = filtered.filter(alert => !alert.resolved)
      }
    }
    
    // 应用当前排序
    if (this.data.sortBy === 'time') {
      filtered.sort((a, b) => b.timestamp - a.timestamp)
    } else if (this.data.sortBy === 'level') {
      const levelOrder = { 'danger': 2, 'warning': 1 }
      filtered.sort((a, b) => {
        const levelDiff = levelOrder[b.level] - levelOrder[a.level]
        if (levelDiff !== 0) return levelDiff
        return b.timestamp - a.timestamp
      })
    }
    
    this.setData({
      filteredAlerts: filtered
    })
  },

  stopPropagation() {
    // 阻止事件冒泡
  }
})
