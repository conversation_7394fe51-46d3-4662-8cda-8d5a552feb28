/* pages/alerts/alerts.wxss */
.container {
  background: #f8f9fa;
  min-height: 100vh;
}

/* 头部 */
.header-section {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  padding: 40rpx 30rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: 700;
}

.filter-btn {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 16rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.filter-icon {
  margin-right: 8rpx;
}

/* 统计概览 */
.stats-overview {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
  padding: 30rpx;
}

.stat-card {
  background: white;
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border-left: 6rpx solid;
}

.stat-danger {
  border-left-color: #dc3545;
}

.stat-warning {
  border-left-color: #ffc107;
}

.stat-resolved {
  border-left-color: #28a745;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.stat-danger .stat-number {
  color: #dc3545;
}

.stat-warning .stat-number {
  color: #ffc107;
}

.stat-resolved .stat-number {
  color: #28a745;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

/* 预警列表 */
.alerts-section {
  background: white;
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.sort-controls {
  display: flex;
  gap: 16rpx;
}

.sort-btn {
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #666;
  transition: all 0.3s ease;
}

.sort-btn.active {
  background: #4A90E2;
  color: white;
}

.alerts-list {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.alert-item {
  padding: 24rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  border-left: 6rpx solid;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.alert-danger {
  background: rgba(220, 53, 69, 0.05);
  border-left-color: #dc3545;
}

.alert-warning {
  background: rgba(255, 193, 7, 0.05);
  border-left-color: #ffc107;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.alert-level-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: white;
}

.level-danger {
  background: #dc3545;
}

.level-warning {
  background: #ffc107;
}

.alert-time {
  font-size: 20rpx;
  color: #999;
}

.alert-content {
  margin-bottom: 20rpx;
}

.alert-message {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
}

.alert-param {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #666;
}

.param-icon {
  margin-right: 8rpx;
  font-size: 20rpx;
}

.alert-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.resolve-btn {
  background: #4A90E2;
  color: white;
  flex: 1;
}

.resolve-btn.resolved {
  background: #28a745;
}

.detail-btn {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #dee2e6;
}

.no-alerts {
  text-align: center;
  padding: 80rpx 20rpx;
  color: #999;
}

.no-alerts-icon {
  font-size: 64rpx;
  display: block;
  margin-bottom: 20rpx;
}

.no-alerts-text {
  font-size: 28rpx;
}

/* 弹窗样式 */
.filter-modal, .detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.filter-modal.show, .detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  overflow: hidden;
  transform: translateY(50rpx);
  transition: transform 0.3s ease;
}

.filter-modal.show .modal-content,
.detail-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  background: #f8f9fa;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
  padding: 10rpx;
}

.modal-body {
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.filter-group {
  margin-bottom: 30rpx;
}

.filter-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.filter-options {
  display: flex;
  gap: 16rpx;
}

.filter-option {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.filter-option.active {
  background: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.btn-primary, .btn-secondary {
  flex: 1;
  padding: 24rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
}

.btn-primary {
  background: #4A90E2;
  color: white;
}

.btn-secondary {
  background: #f8f9fa;
  color: #6c757d;
  border: 2rpx solid #dee2e6;
}

/* 详情弹窗 */
.detail-info {
  margin-top: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.detail-value .param-icon {
  margin-right: 8rpx;
}

.status-resolved {
  color: #28a745;
}

.status-pending {
  color: #dc3545;
}
