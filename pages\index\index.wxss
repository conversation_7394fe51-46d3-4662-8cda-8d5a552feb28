/* pages/index/index.wxss */
.container {
  background: #f7f8fa;
  min-height: 100vh;
}

/* 头部区域 */
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 32rpx 40rpx;
}

.welcome-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.welcome-content {
  flex: 1;
}

.welcome-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.welcome-subtitle {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.system-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-item {
  display: flex;
  align-items: center;
}

.status-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}

.last-update {
  font-size: 22rpx;
  color: #999;
}

.welcome-icon {
  font-size: 64rpx;
  opacity: 0.8;
}

/* 水族箱选择区域 */
.tank-selection {
  padding: 0 32rpx 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.tank-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tank-card {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.tank-card.active {
  border-color: #667eea;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

.tank-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  width: 80rpx;
  text-align: center;
}

.tank-info {
  flex: 1;
}

.tank-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8rpx;
}

.tank-desc {
  font-size: 26rpx;
  color: #646566;
  line-height: 1.4;
}

.tank-arrow {
  font-size: 24rpx;
  color: #c8c9cc;
  margin-left: 16rpx;
}

.tank-card.active .tank-arrow {
  color: #667eea;
}

/* 快速概览 */
.quick-overview {
  padding: 0 32rpx 40rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.overview-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
}

.overview-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
  display: block;
}

.overview-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4rpx;
}

.overview-label {
  font-size: 24rpx;
  color: #646566;
}

.overview-status {
  display: inline-block;
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-left: 8rpx;
}

/* 操作按钮 */
.action-buttons {
  padding: 0 32rpx 40rpx;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
  border: none;
  font-size: 28rpx;
  color: #323233;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 8rpx rgba(100, 101, 102, 0.12);
}

.action-btn-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}

.action-btn-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 水族箱选择区域 */
.tank-selection {
  padding: 0 30rpx 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.refresh-btn {
  padding: 10rpx;
}

.refresh-icon {
  font-size: 28rpx;
  color: white;
  opacity: 0.8;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.tank-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
}

.tank-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  transition: all 0.3s ease;
  border: 3rpx solid transparent;
}

.tank-card.active {
  background: white;
  border-color: #4A90E2;
  transform: translateY(-5rpx);
  box-shadow: 0 8rpx 25rpx rgba(74, 144, 226, 0.3);
}

.tank-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.tank-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.tank-desc {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.param-count {
  font-size: 20rpx;
  color: #4A90E2;
  background: rgba(74, 144, 226, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 环境概览 */
.overview-section {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.overview-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border-left: 6rpx solid #4A90E2;
}

.param-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.param-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.param-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.param-value-section {
  margin-bottom: 12rpx;
}

.param-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #4A90E2;
}

.param-unit {
  font-size: 22rpx;
  color: #666;
  margin-left: 6rpx;
}

.param-status {
  display: flex;
  align-items: center;
}

/* 快速操作 */
.quick-actions {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20rpx;
}

.action-item {
  text-align: center;
  padding: 24rpx 12rpx;
  border-radius: 16rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}

.action-name {
  font-size: 22rpx;
  color: #333;
  font-weight: 500;
}

/* 最近预警 */
.recent-alerts {
  background: white;
  margin: 20rpx 30rpx 40rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.alert-list {
  margin-bottom: 20rpx;
}

.alert-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  border-left: 6rpx solid;
}

.alert-warning {
  background: rgba(255, 193, 7, 0.1);
  border-left-color: #ffc107;
}

.alert-danger {
  background: rgba(220, 53, 69, 0.1);
  border-left-color: #dc3545;
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.alert-time {
  font-size: 22rpx;
  color: #666;
}

.alert-status {
  font-size: 22rpx;
}

.resolved {
  color: #28a745;
}

.pending {
  color: #dc3545;
}

.view-all-alerts {
  text-align: center;
  color: #4A90E2;
  font-size: 26rpx;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
}
