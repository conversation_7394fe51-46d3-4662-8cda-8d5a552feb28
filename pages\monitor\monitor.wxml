<!--pages/monitor/monitor.wxml-->
<view class="container">
  <!-- 头部状态栏 -->
  <view class="status-bar">
    <view class="tank-info">
      <view class="tank-icon">{{tankConfig.icon}}</view>
      <view class="tank-details">
        <view class="tank-name">{{tankConfig.name}}</view>
        <view class="last-update">最后更新: {{lastUpdateTime}}</view>
      </view>
    </view>
    <view class="overall-status status-{{overallStatus}}">
      <view class="status-indicator status-{{overallStatus}}"></view>
      <text>{{overallStatusText}}</text>
    </view>
  </view>

  <!-- 实时参数监控 -->
  <view class="monitor-section">
    <view class="section-header">
      <view class="section-title">实时监控</view>
      <view class="refresh-controls">
        <view class="auto-refresh {{autoRefresh ? 'active' : ''}}" bindtap="toggleAutoRefresh">
          <text class="refresh-icon">🔄</text>
          <text class="refresh-text">{{autoRefresh ? '自动' : '手动'}}</text>
        </view>
        <view class="manual-refresh" bindtap="manualRefresh">
          <text class="refresh-icon {{refreshing ? 'rotating' : ''}}">↻</text>
        </view>
      </view>
    </view>

    <view class="params-grid">
      <view 
        class="param-card param-{{item.status}}" 
        wx:for="{{monitorData}}" 
        wx:key="key"
        bindtap="showParamDetail"
        data-param="{{item.key}}"
      >
        <view class="param-header">
          <view class="param-icon">{{item.icon}}</view>
          <view class="param-info">
            <view class="param-name">{{item.name}}</view>
            <view class="param-range">
              适宜: {{item.optimalMin}}-{{item.optimalMax}}{{item.unit}}
            </view>
          </view>
          <view class="param-status-indicator">
            <view class="status-dot status-{{item.status}}"></view>
          </view>
        </view>
        
        <view class="param-value-display">
          <view class="current-value">
            <text class="value-number">{{item.value}}</text>
            <text class="value-unit">{{item.unit}}</text>
          </view>
          <view class="value-trend">
            <text class="trend-icon trend-{{item.trend}}">{{item.trendIcon}}</text>
            <text class="trend-text">{{item.trendText}}</text>
          </view>
        </view>

        <view class="param-progress">
          <view class="progress-bar">
            <view 
              class="progress-fill progress-{{item.status}}" 
              style="width: {{item.progressPercent}}%"
            ></view>
            <view 
              class="optimal-range" 
              style="left: {{item.optimalStart}}%; width: {{item.optimalWidth}}%"
            ></view>
          </view>
          <view class="progress-labels">
            <text class="min-label">{{item.min}}</text>
            <text class="max-label">{{item.max}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速操作面板 -->
  <view class="quick-panel">
    <view class="panel-title">快速操作</view>
    <view class="quick-actions">
      <view class="quick-action" bindtap="viewHistory">
        <view class="action-icon">📊</view>
        <view class="action-text">历史趋势</view>
      </view>
      <view class="quick-action" bindtap="exportData">
        <view class="action-icon">📤</view>
        <view class="action-text">导出数据</view>
      </view>
      <view class="quick-action" bindtap="setAlert">
        <view class="action-icon">🔔</view>
        <view class="action-text">设置预警</view>
      </view>
      <view class="quick-action" bindtap="calibrateSensor">
        <view class="action-icon">⚙️</view>
        <view class="action-text">传感器校准</view>
      </view>
    </view>
  </view>

  <!-- 系统信息 -->
  <view class="system-info">
    <view class="info-item">
      <text class="info-label">设备状态:</text>
      <text class="info-value status-{{deviceStatus}}">{{deviceStatusText}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">网络连接:</text>
      <text class="info-value status-{{networkStatus}}">{{networkStatusText}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">数据更新间隔:</text>
      <text class="info-value">{{updateInterval}}秒</text>
    </view>
    <view class="info-item">
      <text class="info-label">运行时间:</text>
      <text class="info-value">{{uptime}}</text>
    </view>
  </view>
</view>

<!-- 参数详情弹窗 -->
<view class="param-modal {{showModal ? 'show' : ''}}" bindtap="hideModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <view class="modal-title">
        <text class="param-icon">{{modalData.icon}}</text>
        <text>{{modalData.name}}详情</text>
      </view>
      <view class="close-btn" bindtap="hideModal">×</view>
    </view>
    
    <view class="modal-body">
      <view class="current-reading">
        <view class="reading-label">当前读数</view>
        <view class="reading-value">
          <text class="value">{{modalData.value}}</text>
          <text class="unit">{{modalData.unit}}</text>
        </view>
        <view class="reading-status status-{{modalData.status}}">
          {{modalData.statusText}}
        </view>
      </view>
      
      <view class="param-ranges">
        <view class="range-item">
          <text class="range-label">最小值:</text>
          <text class="range-value">{{modalData.min}}{{modalData.unit}}</text>
        </view>
        <view class="range-item">
          <text class="range-label">最大值:</text>
          <text class="range-value">{{modalData.max}}{{modalData.unit}}</text>
        </view>
        <view class="range-item optimal">
          <text class="range-label">适宜范围:</text>
          <text class="range-value">{{modalData.optimalMin}}-{{modalData.optimalMax}}{{modalData.unit}}</text>
        </view>
      </view>
      
      <view class="recent-trend">
        <view class="trend-title">最近趋势</view>
        <view class="trend-chart">
          <view 
            class="trend-point" 
            wx:for="{{modalData.trendData}}" 
            wx:key="index"
            style="height: {{item.height}}%; background-color: {{item.color}}"
          ></view>
        </view>
      </view>
    </view>
  </view>
</view>
